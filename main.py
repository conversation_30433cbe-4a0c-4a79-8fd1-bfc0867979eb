import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import argparse
from datetime import datetime

# Document processing libraries
from docx import Document
import PyPDF2
import json


class DocumentPageCounter:
    """A class to count pages in DOCX and PDF documents."""

    def __init__(self, use_libreoffice: bool = True):
        self.supported_extensions = {'.docx', '.pdf'}
        self.results = []
        self.errors = []
        self.use_libreoffice = use_libreoffice
        self.libreoffice_available = self._check_libreoffice_available() if use_libreoffice else False

        if use_libreoffice and not self.libreoffice_available:
            print("Warning: LibreOffice not found. Falling back to estimation method for DOCX files.")
            print("For accurate page counts, install LibreOffice and ensure it's in your PATH.")
            self.use_libreoffice = False

    def _check_libreoffice_available(self) -> bool:
        """Check if LibreOffice is available in the system PATH."""
        try:
            # Try different common LibreOffice command names
            commands = ['libreoffice', 'soffice', 'libreoffice7.0', 'libreoffice6.4']
            for cmd in commands:
                try:
                    result = subprocess.run([cmd, '--version'],
                                          capture_output=True,
                                          text=True,
                                          timeout=10)
                    if result.returncode == 0:
                        print(f"Found LibreOffice: {cmd}")
                        self.libreoffice_cmd = cmd
                        return True
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue
            return False
        except Exception:
            return False

    def _convert_docx_to_pdf(self, docx_path: Path) -> Optional[Path]:
        """Convert DOCX to PDF using LibreOffice headless mode."""
        if not self.libreoffice_available:
            return None

        try:
            # Create a temporary directory for the conversion
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir_path = Path(temp_dir)

                # Run LibreOffice conversion
                cmd = [
                    self.libreoffice_cmd,
                    '--headless',
                    '--convert-to', 'pdf',
                    '--outdir', str(temp_dir_path),
                    str(docx_path)
                ]

                result = subprocess.run(cmd,
                                      capture_output=True,
                                      text=True,
                                      timeout=60)

                if result.returncode != 0:
                    self.errors.append(f"LibreOffice conversion failed for {docx_path}: {result.stderr}")
                    return None

                # Find the generated PDF file
                pdf_name = docx_path.stem + '.pdf'
                pdf_path = temp_dir_path / pdf_name

                if pdf_path.exists():
                    # Copy to a new temporary file that won't be deleted immediately
                    temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                    shutil.copy2(pdf_path, temp_pdf.name)
                    temp_pdf.close()
                    return Path(temp_pdf.name)
                else:
                    self.errors.append(f"PDF not generated for {docx_path}")
                    return None

        except subprocess.TimeoutExpired:
            self.errors.append(f"LibreOffice conversion timeout for {docx_path}")
            return None
        except Exception as e:
            self.errors.append(f"LibreOffice conversion error for {docx_path}: {str(e)}")
            return None

    def count_pdf_pages(self, file_path: Path) -> int:
        """Count pages in a PDF file."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                return len(pdf_reader.pages)
        except Exception as e:
            self.errors.append(f"Error reading PDF {file_path}: {str(e)}")
            return 0

    def count_docx_pages(self, file_path: Path) -> int:
        """Count pages in a DOCX file by converting to PDF first (if LibreOffice available)."""
        if self.use_libreoffice and self.libreoffice_available:
            return self._count_docx_pages_via_pdf(file_path)
        else:
            return self._count_docx_pages_estimation(file_path)

    def _count_docx_pages_via_pdf(self, file_path: Path) -> int:
        """Count pages by converting DOCX to PDF first using LibreOffice."""
        try:
            # Convert DOCX to PDF
            temp_pdf_path = self._convert_docx_to_pdf(file_path)
            if temp_pdf_path is None:
                # Fall back to estimation if conversion fails
                return self._count_docx_pages_estimation(file_path)

            try:
                # Count pages in the converted PDF
                page_count = self.count_pdf_pages(temp_pdf_path)
                return page_count
            finally:
                # Clean up temporary PDF file
                try:
                    temp_pdf_path.unlink()
                except:
                    pass

        except Exception as e:
            self.errors.append(f"Error converting DOCX to PDF {file_path}: {str(e)}")
            # Fall back to estimation
            return self._count_docx_pages_estimation(file_path)

    def _count_docx_pages_estimation(self, file_path: Path) -> int:
        """Estimate pages in a DOCX file based on content (fallback method)."""
        try:
            doc = Document(file_path)
            # DOCX doesn't have a direct page count, so we estimate based on content
            # This is an approximation - actual page count depends on formatting

            total_paragraphs = len(doc.paragraphs)
            total_tables = len(doc.tables)

            # Rough estimation: assume ~25 paragraphs per page
            # and each table takes about 1/4 page
            estimated_pages = max(1, (total_paragraphs + total_tables * 6) // 25)

            # Try to get actual page count from document properties if available
            try:
                core_props = doc.core_properties
                if hasattr(core_props, 'pages') and core_props.pages:
                    return core_props.pages
            except:
                pass

            return estimated_pages

        except Exception as e:
            self.errors.append(f"Error reading DOCX {file_path}: {str(e)}")
            return 0

    def count_pages_in_file(self, file_path: Path) -> Tuple[int, str, str]:
        """Count pages in a single file and return (page_count, file_type, method_used)."""
        extension = file_path.suffix.lower()

        if extension == '.pdf':
            return self.count_pdf_pages(file_path), 'PDF', 'Direct'
        elif extension == '.docx':
            if self.use_libreoffice and self.libreoffice_available:
                page_count = self.count_docx_pages(file_path)
                return page_count, 'DOCX', 'LibreOffice'
            else:
                page_count = self.count_docx_pages(file_path)
                return page_count, 'DOCX', 'Estimation'
        else:
            return 0, 'UNSUPPORTED', 'N/A'

    def scan_directory(self, directory_path: Path, recursive: bool = True) -> None:
        """Scan directory for DOCX and PDF files and count their pages."""
        if not directory_path.exists():
            print(f"Error: Directory '{directory_path}' does not exist.")
            return

        if not directory_path.is_dir():
            print(f"Error: '{directory_path}' is not a directory.")
            return

        print(f"Scanning directory: {directory_path}")
        print(f"Recursive scan: {recursive}")
        print("-" * 60)

        # Get all files
        if recursive:
            files = list(directory_path.rglob('*'))
        else:
            files = list(directory_path.iterdir())

        # Filter for supported file types
        document_files = [f for f in files if f.is_file() and f.suffix.lower() in self.supported_extensions]

        if not document_files:
            print("No DOCX or PDF files found in the specified directory.")
            return

        print(f"Found {len(document_files)} document files to process...")
        print()

        total_pages = 0
        file_count_by_type = {'PDF': 0, 'DOCX': 0}
        pages_by_type = {'PDF': 0, 'DOCX': 0}

        for file_path in sorted(document_files):
            try:
                page_count, file_type, method = self.count_pages_in_file(file_path)

                # Calculate relative path for cleaner display
                try:
                    relative_path = file_path.relative_to(directory_path)
                except ValueError:
                    relative_path = file_path

                file_size = file_path.stat().st_size
                file_size_mb = file_size / (1024 * 1024)

                result = {
                    'file_path': str(relative_path),
                    'full_path': str(file_path),
                    'file_type': file_type,
                    'page_count': page_count,
                    'file_size_mb': round(file_size_mb, 2),
                    'counting_method': method,
                    'status': 'SUCCESS' if page_count > 0 else 'ERROR'
                }

                self.results.append(result)

                if page_count > 0:
                    total_pages += page_count
                    file_count_by_type[file_type] += 1
                    pages_by_type[file_type] += page_count

                # Print progress
                status_icon = "✓" if page_count > 0 else "✗"
                method_indicator = f" ({method})" if file_type == 'DOCX' else ""
                print(f"{status_icon} {relative_path}")
                print(f"   Type: {file_type}{method_indicator}, Pages: {page_count}, Size: {file_size_mb:.2f} MB")
                print()

            except Exception as e:
                error_msg = f"Unexpected error processing {file_path}: {str(e)}"
                self.errors.append(error_msg)
                print(f"✗ Error processing {file_path}: {str(e)}")
                print()

        # Print summary
        self.print_summary(total_pages, file_count_by_type, pages_by_type)

    def print_summary(self, total_pages: int, file_count_by_type: Dict[str, int], pages_by_type: Dict[str, int]) -> None:
        """Print a summary of the page counting results."""
        print("=" * 60)
        print("SUMMARY REPORT")
        print("=" * 60)
        print(f"Total files processed: {len(self.results)}")
        print(f"Total pages counted: {total_pages}")
        print()

        print("By file type:")
        for file_type in ['PDF', 'DOCX']:
            if file_count_by_type[file_type] > 0:
                avg_pages = pages_by_type[file_type] / file_count_by_type[file_type]
                print(f"  {file_type}: {file_count_by_type[file_type]} files, {pages_by_type[file_type]} pages (avg: {avg_pages:.1f} pages/file)")

        if self.errors:
            print(f"\nErrors encountered: {len(self.errors)}")
            for error in self.errors:
                print(f"  - {error}")

        print("\n" + "=" * 60)

    def export_report(self, output_file: Path) -> None:
        """Export detailed report to JSON file."""
        report_data = {
            'scan_timestamp': datetime.now().isoformat(),
            'total_files': len(self.results),
            'total_pages': sum(r['page_count'] for r in self.results),
            'files': self.results,
            'errors': self.errors
        }

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            print(f"Detailed report exported to: {output_file}")
        except Exception as e:
            print(f"Error exporting report: {str(e)}")


def main():
    """Main function to run the document page counter."""
    parser = argparse.ArgumentParser(
        description="Count pages in DOCX and PDF files in a directory",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Scan current directory recursively (with LibreOffice)
  python main.py files                    # Scan 'files' directory recursively
  python main.py files --no-recursive     # Scan 'files' directory only (no subdirs)
  python main.py files --output report.json  # Export detailed report to JSON
  python main.py files --no-libreoffice   # Use estimation method for DOCX files
        """
    )

    parser.add_argument(
        'directory',
        nargs='?',
        default='.',
        help='Directory to scan for documents (default: current directory)'
    )

    parser.add_argument(
        '--no-recursive',
        action='store_true',
        help='Do not scan subdirectories recursively'
    )

    parser.add_argument(
        '--output',
        '-o',
        type=str,
        help='Export detailed report to JSON file'
    )

    parser.add_argument(
        '--no-libreoffice',
        action='store_true',
        help='Do not use LibreOffice for DOCX conversion (use estimation instead)'
    )

    args = parser.parse_args()

    # Convert directory path to Path object
    directory_path = Path(args.directory).resolve()

    # Create counter instance and scan
    use_libreoffice = not args.no_libreoffice
    counter = DocumentPageCounter(use_libreoffice=use_libreoffice)
    counter.scan_directory(directory_path, recursive=not args.no_recursive)

    # Export report if requested
    if args.output:
        output_path = Path(args.output)
        counter.export_report(output_path)


if __name__ == "__main__":
    main()
